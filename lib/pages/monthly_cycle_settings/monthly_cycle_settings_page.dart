import 'package:easy_localization/easy_localization.dart';
import 'package:flower_timemachine/controller/nurture_types_controller.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/types/flower_monthly_cycles.dart';

import 'package:flower_timemachine/widgets/ftm_box.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class MonthlyCycleSettingsPage extends StatefulWidget {
  const MonthlyCycleSettingsPage({
    super.key,
    required this.initialCycles,
  });

  final FlowerMonthlyCycles initialCycles;

  @override
  State<MonthlyCycleSettingsPage> createState() => _MonthlyCycleSettingsPageState();
}

class _MonthlyCycleSettingsPageState extends State<MonthlyCycleSettingsPage> {
  late FlowerMonthlyCycles _cycles;
  final List<String> _monthNames = [
    '1月', '2月', '3月', '4月', '5月', '6月',
    '7月', '8月', '9月', '10月', '11月', '12月'
  ];

  @override
  void initState() {
    super.initState();
    _cycles = widget.initialCycles.copy();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        centerTitle: true,
        title: const Text("月份周期设置"),
        elevation: 0,
        actions: [
          TextButton(
            onPressed: _onSave,
            child: const Text("保存").tr(),
          )
        ],
        leading: TextButton(
          onPressed: _onCancel,
          child: const Text("取消").tr(),
        ),
        leadingWidth: 68,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildInstructions(),
            const SizedBox(height: 20),
            _buildNurtureTypesList(),
          ],
        ),
      ),
    );
  }

  Widget _buildInstructions() {
    return FTMBox(
      circular: 10,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "使用说明",
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              "• 继承：使用前一个月的设置，如果前面都是继承则使用默认周期\n"
              "• 未设置：该月不进行此类养护\n"
              "• 具体天数：设置该月的养护间隔天数（1-366天）",
              style: TextStyle(
                fontSize: 14,
                color: Colors.black87,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNurtureTypesList() {
    final types = NurtureTypesController.get().enableTypes;

    return Column(
      children: types.map((type) => _buildNurtureTypeCard(type)).toList(),
    );
  }

  Widget _buildNurtureTypeCard(NurtureType type) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: FTMBox(
        circular: 10,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildNurtureTypeHeader(type),
              const SizedBox(height: 16),
              _buildMonthlyGrid(type),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNurtureTypeHeader(NurtureType type) {
    return Row(
      children: [
        SvgPicture.asset(
          type.icon,
          width: 20,
          height: 20,
        ),
        const SizedBox(width: 8),
        Text(
          type.name,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const Spacer(),
        Text(
          "默认: ${type.defaultCycle == -1 ? '未设置' : '${type.defaultCycle}天'}",
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildMonthlyGrid(NurtureType type) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        childAspectRatio: 2.5,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: 12,
      itemBuilder: (context, index) => _buildMonthItem(type, index + 1),
    );
  }

  Widget _buildMonthItem(NurtureType type, int month) {
    final cycle = _cycles.getCycleForTypeAndMonth(type, month);
    final effectiveCycle = _cycles.getEffectiveCycleForTypeAndMonth(type, month);

    String displayText;
    Color backgroundColor;
    Color textColor = Colors.black87;

    if (cycle == -1) {
      displayText = "未设置";
      backgroundColor = Colors.red[50]!;
      textColor = Colors.red[700]!;
    } else if (cycle == 0) {
      displayText = "继承\n(${effectiveCycle == -1 ? '未设置' : '${effectiveCycle}天'})";
      backgroundColor = Colors.blue[50]!;
      textColor = Colors.blue[700]!;
    } else {
      displayText = "${cycle}天";
      backgroundColor = Colors.green[50]!;
      textColor = Colors.green[700]!;
    }

    return GestureDetector(
      onTap: () => _showCycleSelector(type, month),
      child: Container(
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.grey[300]!,
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _monthNames[month - 1],
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 2),
            Text(
              displayText,
              style: TextStyle(
                fontSize: 10,
                color: textColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showCycleSelector(NurtureType type, int month) {
    final currentCycle = _cycles.getCycleForTypeAndMonth(type, month);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _buildCycleSelectorSheet(type, month, currentCycle),
    );
  }

  Widget _buildCycleSelectorSheet(NurtureType type, int month, int currentCycle) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "${type.name} - ${_monthNames[month - 1]}",
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),

          // 继承选项
          _buildCycleOption(
            title: "继承",
            subtitle: "使用前一个月的设置",
            value: 0,
            currentValue: currentCycle,
            onTap: () => _setCycle(type, month, 0),
          ),

          // 未设置选项
          _buildCycleOption(
            title: "未设置",
            subtitle: "该月不进行此类养护",
            value: -1,
            currentValue: currentCycle,
            onTap: () => _setCycle(type, month, -1),
          ),

          // 自定义天数选项
          _buildCustomCycleOption(type, month, currentCycle),

          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildCycleOption({
    required String title,
    required String subtitle,
    required int value,
    required int currentValue,
    required VoidCallback onTap,
  }) {
    final isSelected = currentValue == value;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : Colors.grey[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? Theme.of(context).primaryColor : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isSelected ? Theme.of(context).primaryColor : Colors.black87,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: Theme.of(context).primaryColor,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomCycleOption(NurtureType type, int month, int currentCycle) {
    final isCustom = currentCycle > 0;
    final controller = TextEditingController(
      text: isCustom ? currentCycle.toString() : '',
    );

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isCustom ? Theme.of(context).primaryColor.withOpacity(0.1) : Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isCustom ? Theme.of(context).primaryColor : Colors.grey[300]!,
          width: isCustom ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "自定义天数",
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: isCustom ? Theme.of(context).primaryColor : Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: controller,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    hintText: "输入天数 (1-366)",
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  ),
                  onSubmitted: (value) {
                    final days = int.tryParse(value);
                    if (days != null && days >= 1 && days <= 366) {
                      _setCycle(type, month, days);
                    }
                  },
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: () {
                  final days = int.tryParse(controller.text);
                  if (days != null && days >= 1 && days <= 366) {
                    _setCycle(type, month, days);
                  } else {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text("请输入1-366之间的数字")),
                    );
                  }
                },
                child: const Text("确定"),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _setCycle(NurtureType type, int month, int cycle) {
    setState(() {
      _cycles.setCycleForTypeAndMonth(type, month, cycle);
    });
    Navigator.pop(context);
  }

  void _onSave() {
    Navigator.pop(context, _cycles);
  }

  void _onCancel() {
    Navigator.pop(context);
  }
}
